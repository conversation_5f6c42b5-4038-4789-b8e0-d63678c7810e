<template>
  <div
    class="upload-area group"
    :class="{
      'border-error': error,
      disabled: isDisabled,
    }"
    @dragover.prevent
    @drop.prevent="handleDrop"
    @click="triggerFileInput"
  >
    <input
      ref="fileInput"
      type="file"
      class="hidden"
      :accept="acceptTypes.join(',')"
      :multiple="multiple"
      @change="handleFileChange"
    />
    <div class="text-gray-600 group-hover:text-primary">
      <slot name="icon">
        <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
          ></path>
        </svg>
      </slot>
      <slot name="title">
        <p class="text-lg mb-2 font-medium">{{ title || '点击或拖拽上传文件' }}</p>
      </slot>
      <slot name="description">
        <p class="text-sm text-gray-500">
          {{ description || `支持 ${acceptTypes.join(', ')} 格式` }}
        </p>
      </slot>
      <p v-if="error" class="text-sm text-destructive mt-2">{{ error }}</p>
    </div>

    <!-- 进度条显示 -->
    <div v-if="isUploading" class="text-center">
      <Progress v-model="uploadProgress" class="w-full my-4" />
      <p class="text-lg text-gray-600">正在处理数据... {{ uploadProgress }}%</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Progress } from '@/components/ui/progress'
/**
 * 文件拖拽上传组件
 * @props
 * acceptTypes - 接受的文件类型数组，例如 ['.xlsx', '.csv']
 * title - 上传区域标题
 * description - 上传区域描述
 * multiple - 是否允许多文件上传
 * maxSize - 最大文件大小(MB)
 * validateFn - 自定义验证函数
 * @emits
 * file-selected - 当文件被选择并通过验证时触发
 * error - 当文件验证失败时触发
 */
const props = defineProps({
  // 接受的文件类型
  acceptTypes: {
    type: Array,
    default: () => ['.xlsx', '.xls', '.csv'],
  },
  // 上传区域标题
  title: {
    type: String,
    default: '',
  },
  // 上传区域描述
  description: {
    type: String,
    default: '',
  },
  // 是否允许多文件上传
  multiple: {
    type: Boolean,
    default: false,
  },
  // 最大文件大小(MB)
  maxSize: {
    type: Number,
    default: 10,
  },
  // 自定义验证函数
  validateFn: {
    type: Function,
    default: null,
  },
  // 是否禁用上传功能
  isDisabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['file-selected', 'error', 'progress', 'upload-complete'])

const fileInput = ref(null)
const error = ref('')
const isUploading = ref(false)
const uploadProgress = ref(0)

// 触发文件选择框
const triggerFileInput = () => {
  if (!props.isDisabled) {
    fileInput.value.click()
  }
}

const processFile = async file => {
  if (!validateFile(file)) return false

  isUploading.value = true
  uploadProgress.value = 0

  // 模拟上传进度
  const progressInterval = setInterval(() => {
    if (uploadProgress.value < 90) {
      uploadProgress.value += 10
      emit('progress', uploadProgress.value)
    }
  }, 200)

  try {
    emit('file-selected', file)

    // 完成上传
    setTimeout(() => {
      clearInterval(progressInterval)
      uploadProgress.value = 100
      emit('progress', 100)

      setTimeout(() => {
        isUploading.value = false
        uploadProgress.value = 0
        emit('upload-complete')
      }, 500)
    }, 1000)

    return true
  } catch (err) {
    clearInterval(progressInterval)
    error.value = '处理文件失败'
    emit('error', error.value)
    isUploading.value = false
    uploadProgress.value = 0
    return false
  }
}

// 文件选择处理方法
const handleFileChange = async e => {
  const files = e.target.files

  if (props.multiple) {
    const validFiles = Array.from(files).filter(file => validateFile(file))
    if (validFiles.length > 0) {
      for (const file of validFiles) {
        await processFile(file)
      }
    }
  } else {
    const file = files[0]
    await processFile(file)
  }

  e.target.value = ''
}

// 拖拽处理方法
const handleDrop = async e => {
  if (props.isDisabled) return
  e.preventDefault()
  const files = e.dataTransfer.files

  if (props.multiple) {
    const validFiles = Array.from(files).filter(file => validateFile(file))
    if (validFiles.length > 0) {
      for (const file of validFiles) {
        await processFile(file)
      }
    }
  } else {
    const file = files[0]
    await processFile(file)
  }
}

// 验证文件
const validateFile = file => {
  error.value = ''

  // 检查文件是否存在
  if (!file) {
    error.value = '请选择文件'
    emit('error', error.value)
    return false
  }

  // 检查文件大小
  const fileSizeMB = file.size / (1024 * 1024)
  if (fileSizeMB > props.maxSize) {
    error.value = `文件大小不能超过 ${props.maxSize}MB`
    emit('error', error.value)
    return false
  }

  // 检查文件类型
  const fileName = file.name.toLowerCase()
  const isValidExtension = props.acceptTypes.some(ext => fileName.endsWith(ext))

  // 检查MIME类型
  const validMimeTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
    'application/csv',
  ]

  if (!isValidExtension && !validMimeTypes.includes(file.type)) {
    error.value = `请上传有效的文件类型: ${props.acceptTypes.join(', ')}`
    emit('error', error.value)
    return false
  }

  // 自定义验证
  if (props.validateFn && typeof props.validateFn === 'function') {
    const customValidation = props.validateFn(file)
    if (customValidation !== true) {
      error.value = customValidation || '文件验证失败'
      emit('error', error.value)
      return false
    }
  }

  return true
}
</script>

<style lang="scss" scoped>
.upload-area {
  border: 2px dashed hsl(var(--border));
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--muted));
}

.upload-area.border-error {
  border-color: hsl(var(--destructive));
}
.upload-area.border-error {
  border-color: hsl(var(--destructive));
}

.upload-area.disabled {
  cursor: not-allowed;
  background-color: hsl(var(--muted) / 0.5);
}
.upload-area:hover:not(.disabled) {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--muted));
}
</style>
