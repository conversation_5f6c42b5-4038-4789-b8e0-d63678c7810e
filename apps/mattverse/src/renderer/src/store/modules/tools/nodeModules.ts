import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { nodeModules, type NodeModule } from '@mattverse/shared'
import { createPersistConfig } from '@/store/plugins/persist-config'

export const useNodeModulesStore = defineStore(
  'nodeModules',
  () => {
    // 存储所有节点模块
    const nodeModulesData = ref<Record<string, NodeModule>>({})

    // 初始化数据 - 确保总是有数据
    const initializeModules = () => {
      // 总是使用默认数据作为基础
      const defaultData = { ...nodeModules }

      // 如果没有数据或数据为空，直接使用默认数据
      if (!nodeModulesData.value || Object.keys(nodeModulesData.value).length === 0) {
        nodeModulesData.value = defaultData
        return
      }

      // 如果有持久化数据，合并默认数据和持久化的启用状态
      Object.keys(defaultData).forEach(key => {
        if (nodeModulesData.value[key]) {
          // 保留持久化的 enabled 状态，其他属性使用默认值
          defaultData[key] = {
            ...defaultData[key],
            enabled: nodeModulesData.value[key].enabled ?? defaultData[key].enabled,
          }
        }
      })

      nodeModulesData.value = defaultData
    }

    // 立即初始化
    initializeModules()

    // 获取所有模块
    const getAllModules = computed(() => {
      return nodeModulesData.value
    })

    // 获取启用的模块
    const getEnabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.enabled)
      )
    })

    // 获取禁用的模块
    const getDisabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => !module.enabled)
      )
    })

    // 获取内置模块
    const getBuiltinModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.isBuiltin)
      )
    })

    // 切换模块启用状态
    const toggleModuleEnabled = (moduleName: string) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = !nodeModulesData.value[moduleName].enabled
      }
    }

    // 设置模块启用状态
    const setModuleEnabled = (moduleName: string, enabled: boolean) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = enabled
      }
    }

    // 获取单个模块
    const getModule = (moduleName: string) => {
      return nodeModulesData.value[moduleName] || null
    }

    // 检查模块是否启用
    const isModuleEnabled = (moduleName: string) => {
      return nodeModulesData.value[moduleName]?.enabled || false
    }

    // 添加新模块
    const addModule = (moduleName: string, moduleData: NodeModule) => {
      nodeModulesData.value[moduleName] = {
        ...moduleData,
        enabled: true,
        isBuiltin: false,
      }
    }

    // 删除模块
    const deleteModule = (moduleName: string) => {
      if (nodeModulesData.value[moduleName] && !nodeModulesData.value[moduleName].isBuiltin) {
        delete nodeModulesData.value[moduleName]
        return true
      }
      return false
    }

    // 获取模块列表（用于展示）
    const getModuleList = computed(() => {
      return Object.entries(nodeModulesData.value).map(([key, module]) => ({
        key,
        name: module.name,
        description: module.description,
        icon: module.icon,
        enabled: module.enabled,
        isBuiltin: module.isBuiltin,
        type: module.type,
        categoriesCount: module.categories?.length || 0,
        nodesCount:
          module.categories?.reduce(
            (total, category) => total + (category.nodes?.length || 0),
            0
          ) || 0,
      }))
    })

    return {
      // 状态
      nodeModulesData,

      // 计算属性
      getAllModules,
      getEnabledModules,
      getDisabledModules,
      getBuiltinModules,
      getModuleList,

      // 方法
      initializeModules,
      toggleModuleEnabled,
      setModuleEnabled,
      getModule,
      isModuleEnabled,
      addModule,
      deleteModule,
    }
  },
  {
    persist: createPersistConfig('nodeModules'),
  }
)
