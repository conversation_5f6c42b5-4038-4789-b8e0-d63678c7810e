import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { nodeModules, type NodeModule } from '@mattverse/shared'
import { createPersistConfig } from '@/store/plugins/persist-config'
import { decode } from '@msgpack/msgpack'
import { nanoid } from 'nanoid'

// 解析 msgpack 数据
const tryDecodeMsgPack = async (
  file: File
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const buffer = await file.arrayBuffer()
    try {
      const decodedData = decode(new Uint8Array(buffer))
      if (decodedData && decodedData.data && typeof decodedData.data === 'string') {
        try {
          const jsonString = decodeURIComponent(escape(atob(decodedData.data)))
          const jsonData = JSON.parse(jsonString)
          return { success: true, data: jsonData }
        } catch {
          // Base64 解码或 JSON 解析失败，继续尝试其他方法
        }
      }
      if (typeof decodedData === 'object' && decodedData !== null) {
        return { success: true, data: decodedData }
      }
    } catch {
      // msgpack 解析失败，尝试其他方法
    }
    try {
      const text = new TextDecoder().decode(buffer)
      if (text.trim().startsWith('{') && text.trim().endsWith('}')) {
        const jsonData = JSON.parse(text)
        return { success: true, data: jsonData }
      }
    } catch {
      // JSON 解析失败
    }
    return {
      success: false,
      error: '无法解析文件格式，请确保文件格式正确',
    }
  } catch (error) {
    return {
      success: false,
      error: `解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
    }
  }
}

// 解析 JSON 数据
const tryParseJSON = async (
  file: File
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const content = await file.text()
    const data = JSON.parse(content)
    return { success: true, data }
  } catch {
    return {
      success: false,
      error: '文件格式错误：请确保文件是有效的JSON格式',
    }
  }
}

export const useNodeModulesStore = defineStore(
  'nodeModules',
  () => {
    // 存储所有节点模块
    const nodeModulesData = ref<Record<string, NodeModule>>({})

    // 初始化数据 - 确保总是有数据
    const initializeModules = () => {
      // 总是使用默认数据作为基础
      const defaultData = { ...nodeModules }

      // 如果没有数据或数据为空，直接使用默认数据
      if (!nodeModulesData.value || Object.keys(nodeModulesData.value).length === 0) {
        nodeModulesData.value = defaultData
        return
      }

      // 如果有持久化数据，合并默认数据和持久化的启用状态
      Object.keys(defaultData).forEach(key => {
        if (nodeModulesData.value[key]) {
          // 保留持久化的 enabled 状态，其他属性使用默认值
          defaultData[key] = {
            ...defaultData[key],
            enabled: nodeModulesData.value[key].enabled ?? defaultData[key].enabled,
          }
        }
      })

      nodeModulesData.value = defaultData
    }

    // 立即初始化
    initializeModules()

    // 获取所有模块
    const getAllModules = computed(() => {
      return nodeModulesData.value
    })

    // 获取启用的模块
    const getEnabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.enabled)
      )
    })

    // 获取禁用的模块
    const getDisabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => !module.enabled)
      )
    })

    // 获取内置模块
    const getBuiltinModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.isBuiltin)
      )
    })

    // 切换模块启用状态
    const toggleModuleEnabled = (moduleName: string) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = !nodeModulesData.value[moduleName].enabled
      }
    }

    // 设置模块启用状态
    const setModuleEnabled = (moduleName: string, enabled: boolean) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = enabled
      }
    }

    // 获取单个模块
    const getModule = (moduleName: string) => {
      return nodeModulesData.value[moduleName] || null
    }

    // 检查模块是否启用
    const isModuleEnabled = (moduleName: string) => {
      return nodeModulesData.value[moduleName]?.enabled || false
    }

    // 添加新模块
    const addModule = (moduleName: string, moduleData: NodeModule) => {
      nodeModulesData.value[moduleName] = {
        ...moduleData,
        enabled: true,
        isBuiltin: false,
      }
    }

    // 删除模块
    const deleteModule = (moduleName: string) => {
      if (nodeModulesData.value[moduleName] && !nodeModulesData.value[moduleName].isBuiltin) {
        delete nodeModulesData.value[moduleName]
        return true
      }
      return false
    }

    // 导入新的节点模块
    const importNodeModule = async (file: File): Promise<{ success: boolean; message: string }> => {
      try {
        const isMsgPack = file.name.endsWith('.plugin')
        let parseResult: { success: boolean; data?: any; error?: string }

        if (isMsgPack) {
          parseResult = await tryDecodeMsgPack(file)
        } else {
          parseResult = await tryParseJSON(file)
        }

        if (!parseResult.success) {
          return {
            success: false,
            message: parseResult.error || '文件解析失败',
          }
        }

        let moduleData = parseResult.data

        // 处理单个模块数据结构
        const keys = Object.keys(moduleData)
        if (keys.length === 1 && typeof moduleData[keys[0]] === 'object' && !moduleData.name) {
          const moduleName = keys[0]
          moduleData = {
            name: moduleName,
            ...moduleData[moduleName],
          }
        }

        // 验证必要字段
        if (!moduleData.name) {
          return { success: false, message: '缺少必要字段：name' }
        }

        if (!moduleData.type) {
          return { success: false, message: '缺少必要字段：type' }
        }

        if (!Array.isArray(moduleData.categories)) {
          return { success: false, message: '缺少必要字段：categories 或格式不正确' }
        }

        // 处理节点数据，为没有ID的节点生成ID
        moduleData.categories.forEach((category: any) => {
          category.nodes.forEach((node: any) => {
            if (!node.id) {
              node.id = nanoid()
            }
          })
        })

        // 添加模块到存储中
        nodeModulesData.value[moduleData.name] = {
          ...moduleData,
          enabled: true,
          isBuiltin: false,
        }

        return { success: true, message: `成功导入节点模块: ${moduleData.name}` }
      } catch (error) {
        return {
          success: false,
          message: `导入失败: ${error instanceof Error ? error.message : '未知错误'}`,
        }
      }
    }

    // 获取模块列表
    const getModuleList = computed(() => {
      return Object.entries(nodeModulesData.value).map(([key, module]) => ({
        key,
        name: module.name,
        description: module.description,
        icon: module.icon,
        enabled: module.enabled,
        isBuiltin: module.isBuiltin,
        type: module.type,
        categoriesCount: module.categories?.length || 0,
        nodesCount:
          module.categories?.reduce(
            (total, category) => total + (category.nodes?.length || 0),
            0
          ) || 0,
      }))
    })

    return {
      // 状态
      nodeModulesData,

      // 计算属性
      getAllModules,
      getEnabledModules,
      getDisabledModules,
      getBuiltinModules,
      getModuleList,

      // 方法
      initializeModules,
      toggleModuleEnabled,
      setModuleEnabled,
      getModule,
      isModuleEnabled,
      addModule,
      deleteModule,
      importNodeModule,
    }
  },
  {
    persist: createPersistConfig('nodeModules'),
  }
)
