<template>
  <div class="p-6 space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-xl font-semibold text-foreground/90">工具模块分组</h1>
      <Button @click="showImportDialog = true" class="flex items-center gap-2">
        <Plus class="w-4 h-4" />
        导入工具
      </Button>
    </div>

    <!-- 空数据状态 -->
    <div v-if="moduleList.length === 0" class="flex items-center justify-center min-h-[60vh]">
      <MattEmptyState
        icon="Package"
        title="暂无工具模块"
        description='请点击右上角的"导入工具"按钮导入工具模块'
      />
    </div>

    <!-- 工具模块列表 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5">
      <Card
        v-for="module in moduleList"
        :key="module.key"
        class="group hover:shadow-lg transition-all duration-300 relative overflow-hidden border border-border/40 bg-gradient-to-br from-card to-card/95"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-slate-900/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
        ></div>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-lg font-medium transition-colors duration-300">
            {{ module.name }}
          </CardTitle>
          <div
            class="rounded-full bg-primary/10 p-2.5 transition-all duration-300 group-hover:scale-110 group-hover:bg-primary/20"
          >
            <MattSvg
              v-if="isSvg(module.icon)"
              :name="module.icon.value"
              class-name="w-5 h-5"
              :title="`SVG: ${module.icon.value}`"
            />
            <MattIcon
              v-else-if="isIcon(module.icon)"
              :name="module.icon.value"
              class="w-5 h-5"
              :title="`Icon: ${module.icon.value}`"
            />
            <span v-else class="text-base" title="Default icon">🔧</span>
          </div>
        </CardHeader>

        <CardContent>
          <p
            class="text-sm text-muted-foreground line-clamp-2 h-10 group-hover:text-foreground/90 transition-colors duration-300"
          >
            {{ module.description || '暂无描述' }}
          </p>
          <div class="flex items-center justify-between mt-4">
            <span
              class="text-sm font-medium text-gray-500 group-hover:text-gray-700 transition-colors duration-300"
            >
              是否启用
            </span>
            <Switch
              :checked="!!module.enabled"
              class="transition-all duration-300"
              @update:checked="v => onToggle(module.key, v)"
            />
          </div>
        </CardContent>

        <!-- 删除按钮 -->
        <Button
          v-if="!module.isBuiltin"
          variant="destructive"
          size="icon"
          class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-300 scale-90 group-hover:scale-100 hover:bg-destructive/90"
          @click="openDeleteDialog(module.key)"
        >
          <Trash class="h-4 w-4" />
        </Button>

        <!-- 内置模块标记 -->
        <Badge
          v-if="module.isBuiltin"
          class="absolute top-1 left-2 bg-gray-400/80 backdrop-blur-sm transition-all duration-300"
        >
          内置
        </Badge>
      </Card>
    </div>

    <!-- 删除确认对话框 -->
    <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除"{{ moduleToDelete }}"工具模块吗？此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
          <AlertDialogAction @click="confirmDelete">确认删除</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    <!-- 导入工具对话框 -->
    <Dialog :open="showImportDialog" @update:open="showImportDialog = $event">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>导入工具模块</DialogTitle>
          <DialogDescription> 请选择一个工具模块文件 (.plugin) 进行导入 </DialogDescription>
        </DialogHeader>

        <div class="py-4">
          <FileDropUpload
            :accept-types="['.plugin']"
            :max-size="5"
            @file-selected="handleFileSelected"
            @error="handleUploadError"
          />
        </div>

        <DialogFooter>
          <Button variant="outline" @click="showImportDialog = false"> 取消 </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useNodeModulesStore } from '@/store'
import { Trash, Plus } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { FileDropUpload } from '@mattverse/mattverse-ui'

const toolsStore = useNodeModulesStore()
const { getModuleList: moduleList } = storeToRefs(toolsStore)

// 组件挂载时确保数据初始化
onMounted(() => {
  toolsStore.initializeModules()
})

// 删除对话框状态
const isDeleteDialogOpen = ref(false)
const moduleToDelete = ref('')

// 导入对话框状态
const showImportDialog = ref(false)

function onToggle(name: string, value: boolean) {
  toolsStore.setModuleEnabled(name, value)
  toast.info(value ? '已启用' : '已禁用', {
    description: `${name} 已${value ? '启用' : '禁用'}`,
  })
}

function isSvg(icon: any): icon is { type: 'svg'; value: string } {
  return icon && typeof icon === 'object' && icon.type === 'svg' && typeof icon.value === 'string'
}

function isIcon(icon: any): icon is { type: 'icon'; value: string } {
  return icon && typeof icon === 'object' && icon.type === 'icon' && typeof icon.value === 'string'
}

// 打开删除对话框
const openDeleteDialog = (moduleKey: string) => {
  moduleToDelete.value = moduleKey
  isDeleteDialogOpen.value = true
}

// 确认删除
const confirmDelete = () => {
  // 这里应该调用删除模块的方法
  // toolsStore.deleteModule(moduleToDelete.value)

  toast.success('删除成功', {
    description: `成功删除工具模块 ${moduleToDelete.value}`,
  })

  isDeleteDialogOpen.value = false
  moduleToDelete.value = ''
}

// 文件上传处理方法
const handleFileSelected = (file: File) => {
  toast.info('开始处理文件', {
    description: `正在处理 ${file.name}`,
  })
  // TODO: 实现实际的文件处理逻辑
}

const handleUploadError = (error: string) => {
  toast.error('上传失败', {
    description: error,
  })
}
</script>

<style lang="scss" scoped>
.card {
  @apply backdrop-blur-sm;

  &:hover {
    @apply shadow-md shadow-primary/5;
  }
}

.badge {
  @apply text-xs font-medium px-2 py-0.5 rounded-full;
}
</style>
